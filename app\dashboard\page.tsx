"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { CompactUpload } from "@/components/dashboard/compact-upload"
import { useSidebar } from "@/components/ui/sidebar"
import { useSidebarContext } from "@/components/sidebar-context"
import { Button } from "@/components/ui/button"
import { Loader2, ArrowUp, PanelLeft } from "lucide-react"
import { AIThinking } from "@/components/ui/ai-thinking"
import { useDocumentPersistence } from "@/hooks/use-document-persistence"
import { sessionStateManager } from "@/lib/session-state"
import { toast } from "sonner"
import { ConnectionStatus } from "@/components/ui/connection-status"
import { OfflineBanner } from "@/components/ui/offline-banner"

interface Message {
  [x: string]: any
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: string
  isThinking?: boolean
}

export default function DashboardPage() {
  const { activeDocument, setActiveDocument, isLoading: documentLoading } = useDocumentPersistence()
  const [selectedDocument, setSelectedDocument] = useState<{ id: string; name: string } | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [loading, setLoading] = useState(false)
  const [sessionLoading, setSessionLoading] = useState(false)
  const [currentChunk, setCurrentChunk] = useState(0)
  const [totalChunks, setTotalChunks] = useState(0)

  // Enhanced setMessages that also saves to localStorage
  const setMessagesWithStorage = (updater: React.SetStateAction<Message[]>) => {
    setMessages(prevMessages => {
      const newMessages = typeof updater === 'function' ? updater(prevMessages) : updater

      // Save to localStorage if we have a selected document
      if (selectedDocument && newMessages.length > 0) {
        sessionStateManager.saveConversationToStorage(selectedDocument.id, newMessages)
      }

      return newMessages
    })
  }
  const { setOpenMobile } = useSidebar()
  const { sidebarVisible, setSidebarVisible } = useSidebarContext()
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // This effect listens for document selection changes from the sidebar
  // Create stable event handlers using useCallback
  const handleDocumentSelected = useCallback(async (event: Event) => {
    const customEvent = event as CustomEvent<{ id: string; name: string }>
    const documentInfo = customEvent.detail

    setSelectedDocument(documentInfo)
    setOpenMobile(false) // Close sidebar on mobile after selection

    if (documentInfo) {
      // Update persistent state
      await setActiveDocument(documentInfo)
      initializeChat(documentInfo.id)
    }
  }, [setActiveDocument, setOpenMobile])

  const handleNavigateToUpload = useCallback(async () => {
    setSelectedDocument(null)
    setMessages([])
    await setActiveDocument(null)
    setOpenMobile(false)
  }, [setActiveDocument, setOpenMobile])

  const handleDocumentDeleted = useCallback(async (event: Event) => {
    const customEvent = event as CustomEvent<{ documentId: string }>
    const deletedDocumentId = customEvent.detail.documentId

    // Clear session state for deleted document
    sessionStateManager.clearSession(deletedDocumentId)

    // If the currently selected document was deleted, clear the selection and redirect to upload
    if (selectedDocument && selectedDocument.id === deletedDocumentId) {
      console.log('🗑️ Currently active document was deleted, redirecting to upload interface')

      setSelectedDocument(null)
      setMessages([])
      setCurrentChunk(0)
      setTotalChunks(0)
      await setActiveDocument(null)

      // Show feedback to user
      toast.info('Document deleted. Upload a new document to continue learning.')
    }

    // Document list will refresh automatically via the documentDeleted event
  }, [selectedDocument, setActiveDocument])

  useEffect(() => {
    window.addEventListener("documentSelected", handleDocumentSelected as EventListener)
    window.addEventListener("navigateToUpload", handleNavigateToUpload as EventListener)
    window.addEventListener("documentDeleted", handleDocumentDeleted as EventListener)

    return () => {
      window.removeEventListener("documentSelected", handleDocumentSelected as EventListener)
      window.removeEventListener("navigateToUpload", handleNavigateToUpload as EventListener)
      window.removeEventListener("documentDeleted", handleDocumentDeleted as EventListener)
    }
  }, [handleDocumentSelected, handleNavigateToUpload, handleDocumentDeleted])

  // Sync with persistent document state
  useEffect(() => {
    if (!documentLoading && activeDocument) {
      console.log('🔄 Restoring active document:', activeDocument.name)
      setSelectedDocument(activeDocument)
      initializeChat(activeDocument.id)
    }
  }, [activeDocument, documentLoading])

  // Auto-resize textarea when input changes
  useEffect(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current
      textarea.style.height = 'auto'
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }
  }, [input])

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesContainerRef.current) {
        const container = messagesContainerRef.current
        container.scrollTop = container.scrollHeight
      }
    }

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(scrollToBottom, 50)
    return () => clearTimeout(timeoutId)
  }, [messages])

  const initializeChat = async (documentId: string) => {
    setSessionLoading(true)
    try {
      console.log('🔄 Initializing chat for document:', documentId)

      // First, check if document is ready
      console.log('🔍 Checking document status...')
      try {
        const statusResponse = await fetch(`/api/documents/${documentId}/status`)
        if (statusResponse.ok) {
          const statusData = await statusResponse.json()
          console.log('📊 Document status:', statusData.status)

          if (statusData.status === 'PROCESSING') {
            console.log('⏳ Document still processing, will retry in a moment...')
            setMessages([{
              id: `processing_${Date.now()}`,
              role: "system" as const,
              content: "📄 Your document is still being processed. This usually takes a few moments...",
              timestamp: new Date().toISOString()
            }])

            // Retry after a delay
            setTimeout(() => {
              initializeChat(documentId)
            }, 3000)
            return
          } else if (statusData.status === 'ERROR') {
            console.error('❌ Document processing failed:', statusData.errorMessage)
            setMessages([{
              id: `error_${Date.now()}`,
              role: "system" as const,
              content: `❌ Document processing failed: ${statusData.errorMessage || 'Unknown error'}`,
              timestamp: new Date().toISOString()
            }])
            return
          }
          // If status is 'READY', continue with initialization
        } else {
          console.warn('⚠️ Could not check document status, proceeding anyway')
        }
      } catch (statusError) {
        console.warn('⚠️ Status check failed, proceeding anyway:', statusError)
      }

      // Check if we already have a valid session for this document
      const existingSession = sessionStateManager.getSessionState(documentId)
      const hasValidSession = sessionStateManager.isSessionInitialized(documentId)

      console.log('🔍 Session check:', {
        hasValidSession,
        hasMessages: existingSession?.hasMessages,
        sessionId: existingSession?.sessionId
      })

      // Only clear session if it's expired or invalid, not on every initialization
      if (existingSession && !hasValidSession) {
        console.log('🧹 Clearing expired session state')
        sessionStateManager.clearSession(documentId)
      }

      // Try to initialize/restore session via the chat initialize endpoint
      const response = await fetch(`/api/chat/initialize`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ documentId }),
      })

      if (response.ok) {
        const responseText = await response.text()
        console.log('🔍 Raw API response:', responseText)

        let data
        try {
          data = JSON.parse(responseText)
        } catch (parseError) {
          console.error('❌ JSON Parse Error:', parseError)
          console.error('❌ Response text that failed to parse:', responseText)
          toast.error('Failed to parse server response')
          return
        }

        // Update session state manager
        sessionStateManager.markSessionInitialized(
          documentId,
          data.sessionId,
          data.messages && data.messages.length > 0,
          data.currentChunk || 0,
          data.totalChunks || 0
        )

        // Enhanced session restoration logic - prioritize localStorage over Mem0
        console.log('🔍 Session restoration check:', {
          hasExistingSession: data.hasExistingSession,
          messagesCount: data.messages?.length || 0,
          sessionId: data.sessionId,
          currentChunk: data.currentChunk
        })

        // First, try to restore from localStorage (fast and reliable)
        const localMessages = sessionStateManager.loadConversationFromStorage(documentId)
        const localSessionHasMessages = sessionStateManager.hasExistingMessages(documentId)

        // Check if we have actual conversation history from any source
        const hasRealConversation = data.messages && data.messages.length > 0 &&
          data.messages.some((msg: Message) =>
            msg.role === 'user' && msg.content.length > 50 // Look for substantial user messages
          )

        console.log('🔍 Conversation analysis:', {
          localMessagesCount: localMessages.length,
          hasRealConversation,
          localSessionHasMessages,
          shouldRestore: localMessages.length > 0 || data.hasExistingSession || hasRealConversation
        })

        // Prioritize localStorage messages over Mem0 messages for UI restoration
        if (localMessages.length > 0) {
          // Restore from localStorage (preferred method)
          console.log(`📱 Restoring conversation from localStorage: ${localMessages.length} messages`)
          setMessages(localMessages) // Don't save to storage again when restoring
          setCurrentChunk(data.currentChunk || 0)
          setTotalChunks(data.totalChunks || 0)
          return // Don't process first chunk again
        } else if (data.hasExistingSession || hasRealConversation) {
          // Fallback to Mem0 messages if localStorage is empty
          console.log(`📚 Restoring conversation from Mem0: ${data.messages?.length || 0} messages`)
          const messagesToRestore = data.messages || []
          setMessages(messagesToRestore) // Don't save to storage again when restoring
          setCurrentChunk(data.currentChunk || 0)
          setTotalChunks(data.totalChunks || 0)

          // Save to localStorage for future fast restoration
          if (messagesToRestore.length > 0) {
            sessionStateManager.saveConversationToStorage(documentId, messagesToRestore)
          }
          return // Don't process first chunk again
        } else {
          // NEW SESSION - Display chunk first, then trigger AI response separately
          console.log('🆕 New session - displaying first chunk')
          
          // Clear any old messages and set basic state
          setMessages([]) // Clear without saving to storage
          setCurrentChunk(data.currentChunk || 0)
          setTotalChunks(data.totalChunks || 0)

          // Step 1: Display the first chunk immediately
          const chunkMessage = {
            id: `chunk_${Date.now()}`,
            role: "user" as const,
            content: data.chunkContent || "Let's start learning about this document.",
            timestamp: new Date().toISOString()
          }

          setMessagesWithStorage([chunkMessage]) // Save to storage for new sessions
          console.log('✅ First chunk displayed, now triggering AI response...')
          
          // Step 2: After a short delay, trigger AI response (non-blocking)
          // Note: The session is already initialized by the /api/chat/initialize call above
          setTimeout(() => {
            triggerAIResponse(documentId, data.chunkContent)
          }, 500) // Small delay to let chunk render first
          
          return
        }
      } else {
        const errorData = await response.json()

        if (errorData.needsInitialization) {
          // AI needs to be initialized first - this is a new document
          console.log('🆕 Document needs AI initialization')

          // First, initialize the AI session using the AI initialize endpoint
          console.log('🤖 Initializing AI session for new document...')
          try {
            const aiInitResponse = await fetch('/api/ai/initialize', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ documentId })
            })

            if (aiInitResponse.ok) {
              console.log('✅ AI session initialized successfully')

              // Now that AI is initialized, retry the chat initialization
              console.log('🔄 Retrying chat initialization...')
              setTimeout(() => {
                initializeChat(documentId)
              }, 1000)
              return
            } else {
              const aiError = await aiInitResponse.json()
              console.error('❌ AI initialization failed:', aiError)

              throw new Error(aiError.error || 'AI initialization failed')
            }
          } catch (aiInitError) {
            console.error('❌ AI initialization error:', aiInitError)

            setMessages([{
              id: `error_${Date.now()}`,
              role: "system" as const,
              content: `❌ Failed to initialize AI: ${aiInitError instanceof Error ? aiInitError.message : 'Unknown error'}`,
              timestamp: new Date().toISOString()
            }])
          }
          return
        }

        throw new Error(errorData.error || "Failed to initialize session")
      }
    } catch (error) {
      console.error("Failed to initialize session:", error)

      // Fallback: try to get basic document info and process first chunk
      try {
        const docResponse = await fetch(`/api/documents/${documentId}`)
        if (docResponse.ok) {
          const docData = await docResponse.json()
          setCurrentChunk(0)
          setTotalChunks(docData.totalChunks || 0)
          setMessages([])
          
          // Process first chunk even in fallback scenario
          console.log('🔄 Fallback: Processing first chunk')
          await triggerAIResponse(documentId, null)
        }
      } catch (fallbackError) {
        console.error("Fallback initialization failed:", fallbackError)
        toast.error("Failed to initialize chat session")
      }
    } finally {
      setSessionLoading(false)
    }
  }

  // Remove the old processFirstChunkWithAI function - it's replaced by triggerAIResponse

  const sendMessage = async () => {
    if (!input.trim() || loading || !selectedDocument) return

    const userMessage = input.trim()
    setInput("")
    setLoading(true)

    // Scroll to bottom immediately when user sends message
    setTimeout(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
      }
    }, 100)

    // Add user message immediately
    const newUserMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: userMessage,
      timestamp: new Date().toISOString(),
    }
    setMessagesWithStorage((prev) => [...prev, newUserMessage])

    // Add thinking indicator
    const thinkingMessage: Message = {
      id: `thinking_${Date.now()}`,
      role: "assistant",
      content: "thinking",
      timestamp: new Date().toISOString(),
      isThinking: true
    }
    setMessagesWithStorage((prev) => [...prev, thinkingMessage])

    try {
      const response = await fetch("/api/chat/message", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId: selectedDocument.id,
          chunkIndex: currentChunk,
          message: userMessage,
        }),
      })

      if (!response.ok) throw new Error("Failed to send message")

      const data = await response.json()

      // Replace thinking message with AI response
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.response,
        timestamp: new Date().toISOString(),
      }
      setMessagesWithStorage((prev) =>
        prev.map(msg =>
          msg.isThinking ? aiMessage : msg
        )
      )

      // Update session state to indicate we have messages
      if (selectedDocument) {
        sessionStateManager.updateMessageCount(selectedDocument.id, true)
      }
    } catch (error) {
      console.error("Failed to send message:", error)
      // Remove thinking message on error
      setMessages((prev) => prev.filter(msg => !msg.isThinking))
    } finally {
      setLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const handleUploadComplete = async (documentId: string, filename: string) => {
    // Automatically select the uploaded document and start chat
    const documentInfo = { id: documentId, name: filename }
    setSelectedDocument(documentInfo)
    await setActiveDocument(documentInfo)
    initializeChat(documentId)
  }

  const handleNextChunk = async () => {
    if (!selectedDocument || currentChunk >= totalChunks - 1) return

    setLoading(true)
    try {
      console.log(`🔄 Loading next chunk from current: ${currentChunk} to ${currentChunk + 1}`)

      const response = await fetch(`/api/chat/next-chunk`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId: selectedDocument.id,
          currentChunk: currentChunk, // Pass current chunk, API will calculate next
          direction: "next"
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("Next chunk error:", errorData)

        // If there's a currentChunk mismatch, sync with server
        if (errorData.currentChunk !== undefined) {
          setCurrentChunk(errorData.currentChunk)
        }

        throw new Error(errorData.error || "Failed to load next chunk")
      }

      const data = await response.json()

      // Update chunk index (ensure it's sequential)
      if (data.currentChunk === currentChunk + 1) {
        setCurrentChunk(data.currentChunk)
      } else {
        console.warn(`Unexpected chunk progression: expected ${currentChunk + 1}, got ${data.currentChunk}`)
        setCurrentChunk(data.currentChunk) // Sync with server anyway
      }

      // Add the new chunk content as a user message (right side)
      const chunkMessage = {
        id: `chunk_${data.currentChunk}_${Date.now()}`,
        role: "user" as const,
        content: data.chunkContent,
        timestamp: new Date().toISOString()
      }

      // Add AI response as assistant message (left side)
      const aiMessage = {
        id: `ai_chunk_${data.currentChunk}_${Date.now()}`,
        role: "assistant" as const,
        content: data.aiResponse,
        timestamp: new Date().toISOString()
      }

      // Append to existing messages (don't replace)
      setMessages(prev => [...prev, chunkMessage, aiMessage])

      // Update session state
      if (selectedDocument) {
        sessionStateManager.updateCurrentChunk(selectedDocument.id, data.currentChunk)
        sessionStateManager.updateMessageCount(selectedDocument.id, true)
      }

      console.log(`✅ Successfully loaded chunk ${data.currentChunk} and added to conversation`)

      // Show success feedback
      toast.success(`Moved to section ${data.currentChunk + 1} of ${data.totalChunks}`)

    } catch (error) {
      console.error("Failed to load next chunk:", error)
      toast.error(error instanceof Error ? error.message : "Failed to load next section")
    } finally {
      setLoading(false)
    }
  }

  const triggerAIResponse = async (documentId: string, chunkContent: string | null) => {
    console.log('🤖 Starting AI response for first chunk')
    setLoading(true)
    
    try {
      // Step 1: Display the first chunk immediately (if we have content)
      if (chunkContent) {
        const chunkMessage = {
          id: `chunk_${Date.now()}`,
          role: "user" as const,
          content: chunkContent,
          timestamp: new Date().toISOString()
        }
        
        setMessages([chunkMessage])
        console.log('✅ First chunk displayed')
      }

      // Step 2: Add AI thinking indicator immediately (no delay)
      const thinkingMessage = {
        id: `thinking_${Date.now()}`,
        role: "assistant" as const,
        content: "thinking",
        timestamp: new Date().toISOString(),
        isThinking: true
      }

      setMessagesWithStorage(prev => [...prev, thinkingMessage])
      console.log('🤔 AI thinking indicator added immediately')

      // Step 3: Send to AI via streaming
      const response = await fetch("/api/chat/stream", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId,
          message: chunkContent || "Let's start learning about this document.",
          chunkIndex: 0
        }),
      })

      if (!response.ok) {
        // Try to get more detailed error information
        let errorMessage = `Failed to get AI response: ${response.status}`
        try {
          const errorData = await response.json()
          if (errorData.error) {
            errorMessage = errorData.error
          }
          console.error('❌ AI response error details:', errorData)
        } catch (parseError) {
          console.error('❌ Could not parse error response')
        }
        throw new Error(errorMessage)
      }

      // Handle streaming response
      const reader = response.body?.getReader()
      if (!reader) throw new Error("No response body")

      const aiMessage = {
        id: `ai_${Date.now()}`,
        role: "assistant" as const,
        content: "",
        timestamp: new Date().toISOString()
      }

      // Replace thinking message with AI response
      setMessagesWithStorage(prev =>
        prev.map(msg =>
          msg.isThinking ? aiMessage : msg
        )
      )

      // Stream the response
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'token' && data.content) {
                setMessagesWithStorage(prev =>
                  prev.map(msg =>
                    msg.id === aiMessage.id
                      ? { ...msg, content: msg.content + data.content }
                      : msg
                  )
                )
              }
            } catch (e) {
              console.warn('Parse error:', e)
            }
          }
        }
      }

    } catch (error) {
      console.error("Failed to get AI response:", error)
      // Remove thinking message and show error
      setMessagesWithStorage(prev => prev.filter(msg => !msg.isThinking))

      const errorMessage = {
        id: `error_${Date.now()}`,
        role: "assistant" as const,
        content: "I'm having trouble processing this content. Please try asking me a question.",
        timestamp: new Date().toISOString()
      }
      setMessagesWithStorage(prev => [...prev, errorMessage])
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Offline Banner */}
      <OfflineBanner />
      
      {/* Connection Status Indicator */}
      <ConnectionStatus />
      
      {/* Fixed Navbar - Using unified color #F9FAFB */}
      <div className="bg-[#F9FAFB] px-3 py-1 flex-shrink-0 flex items-center min-h-[48px]">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setSidebarVisible(!sidebarVisible)}
          className="h-8 w-8 p-1 hover:bg-gray-200"
        >
          <PanelLeft className="h-4 w-4" />
        </Button>
        {selectedDocument && (
          <div className="px-6 py-4 flex items-center justify-end">
            <div className="flex items-center gap-4">
              <div className="text-xs text-gray-600 font-body">
                Section {currentChunk + 1} of {totalChunks}
              </div>
              <Button
                onClick={handleNextChunk}
                disabled={currentChunk >= totalChunks - 1 || loading}
                className="bg-primary hover:bg-primary/90 text-white px-3 py-1 text-xs font-body disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-3 h-3 animate-spin mr-1" />
                    Loading...
                  </>
                ) : currentChunk >= totalChunks - 1 ? (
                  "Completed"
                ) : (
                  `Next Section →`
                )}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Main Content Area */}
      {selectedDocument ? (
        // Chat interface
        <div className="flex-1 flex flex-col bg-white overflow-hidden">
          {/* Messages Area - PROPERLY Scrollable */}
          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto pb-32 hide-scrollbar"
            style={{
              height: 'calc(100vh - 120px)',
              scrollBehavior: 'smooth'
            }}
          >
            {sessionLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                  <p className="text-gray-600 font-body">Starting your study session...</p>
                </div>
              </div>
            ) : (
              <div className="w-full px-6 py-6">
                {messages.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-gray-600 font-body text-lg">
                      Start a conversation about this document...
                    </p>
                  </div>
                )}
                {/* Match the input box width constraint */}
                <div className="max-w-2xl mx-auto space-y-6">
                  {messages.map((message) => (
                    <div key={message.id}>
                      {message.role === "user" ? (
                        // User message - Right aligned within the container
                        <div className="flex justify-end w-full pr-4">
                          <div className="max-w-[85%] bg-gray-100 text-gray-900 rounded-2xl px-4 py-3 font-body">
                            <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                          </div>
                        </div>
                      ) : (
                        // AI message - Left aligned but with proper spacing to match input field
                        <div className="flex justify-start w-full">
                          <div className="w-full max-w-full text-gray-900 font-body pl-4">
                            {message.isThinking ? (
                              <AIThinking />
                            ) : (
                              <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  {/* Scroll anchor */}
                  <div ref={messagesEndRef} />

                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        // Upload interface
        <div className="flex-1 flex items-center justify-center bg-white">
          <div className="text-center max-w-md">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-gray-900 mb-2 font-heading">Welcome to Guided Tutor</h1>
              <p className="text-gray-600 font-body">Upload your first document to start learning</p>
            </div>
            <CompactUpload onUploadComplete={handleUploadComplete} />
          </div>
        </div>
      )}

      {/* Fixed Input Area - Only show when document is selected */}
      {selectedDocument && (
        <div
          className="fixed bottom-0 bg-white z-10"
          style={{
            left: sidebarVisible ? '260px' : '0px',
            right: '0px',
            width: sidebarVisible ? 'calc(100vw - 260px)' : '100vw',
            padding: '1.5rem',
            margin: '0',
            boxSizing: 'border-box'
          }}
        >
          <div className="max-w-2xl mx-auto">
            <div className="relative flex items-end bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 focus-within:border-gray-400">
              <textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Teach me about..."
                disabled={loading}
                rows={1}
                className="flex-1 bg-transparent border-0 px-0 py-0 font-body text-gray-900 placeholder-gray-500 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:outline-none resize-none overflow-y-auto"
                style={{
                  minHeight: '24px',
                  maxHeight: '120px',
                  lineHeight: '24px'
                }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement
                  target.style.height = 'auto'
                  target.style.height = Math.min(target.scrollHeight, 120) + 'px'
                }}
              />
              <Button
                onClick={sendMessage}
                disabled={loading || !input.trim()}
                size="icon"
                className="ml-2 bg-primary hover:bg-primary/90 text-white rounded-full h-8 w-8 flex-shrink-0"
              >
                <ArrowUp className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
